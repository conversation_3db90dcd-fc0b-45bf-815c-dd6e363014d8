<template>
  <Layout>
    <div class="dashboard">
      <div class="dashboard-header">
        <h1>工作台</h1>
        <p>欢迎回来，{{ authStore.user?.username }}！</p>
      </div>
      
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">待处理工单</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon processing">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.processing }}</div>
                <div class="stat-label">处理中工单</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completed }}</div>
                <div class="stat-label">已完成工单</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总工单数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 快速操作 -->
      <el-row :gutter="20" class="quick-actions">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>快速操作</span>
            </template>
            <div class="action-buttons">
              <el-button type="primary" @click="$router.push('/work-orders/create')">
                <el-icon><Plus /></el-icon>
                创建工单
              </el-button>
              <el-button @click="$router.push('/work-orders')">
                <el-icon><List /></el-icon>
                查看工单
              </el-button>
              <el-button v-if="authStore.isAdmin || authStore.isManager" @click="$router.push('/personnel')">
                <el-icon><User /></el-icon>
                人员管理
              </el-button>
              <el-button v-if="authStore.isAdmin || authStore.isManager" @click="$router.push('/vehicles')">
                <el-icon><Van /></el-icon>
                车辆管理
              </el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>我的工单</span>
            </template>
            <div class="my-orders">
              <div v-if="myOrders.length === 0" class="empty-state">
                暂无工单
              </div>
              <div v-else>
                <div
                  v-for="order in myOrders"
                  :key="order.id"
                  class="order-item"
                  @click="$router.push(`/work-orders/${order.id}`)"
                >
                  <div class="order-title">{{ order.title }}</div>
                  <div class="order-meta">
                    <el-tag :type="getStatusType(order.status)" size="small">
                      {{ getStatusText(order.status) }}
                    </el-tag>
                    <span class="order-time">{{ formatTime(order.created_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Clock,
  Loading,
  Check,
  Document,
  Plus,
  List,
  User,
  Van
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import { getWorkOrderStats, getWorkOrders, type WorkOrder } from '@/api/workOrders'
import dayjs from 'dayjs'

const authStore = useAuthStore()

const stats = ref({
  pending: 0,
  processing: 0,
  completed: 0,
  total: 0
})

const myOrders = ref<WorkOrder[]>([])

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    assigned: 'info',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    assigned: '已分配',
    in_progress: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const loadDashboardData = async () => {
  try {
    // 加载统计数据
    const statsResponse = await getWorkOrderStats()
    stats.value = {
      pending: statsResponse.pending,
      processing: statsResponse.in_progress,
      completed: statsResponse.completed,
      total: statsResponse.total
    }

    // 加载我的工单（最近的5个）
    const myOrdersParams = authStore.isAdmin || authStore.isManager
      ? { per_page: 5 }
      : { assignee_id: authStore.user?.id, per_page: 5 }

    const ordersResponse = await getWorkOrders(myOrdersParams)
    myOrders.value = ordersResponse.work_orders
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
}

.dashboard-header p {
  margin: 0;
  color: #666;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background-color: #e6a23c;
}

.stat-icon.processing {
  background-color: #409eff;
}

.stat-icon.completed {
  background-color: #67c23a;
}

.stat-icon.total {
  background-color: #909399;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.quick-actions {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.my-orders {
  max-height: 300px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.order-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.order-item:hover {
  background-color: #f9f9f9;
}

.order-item:last-child {
  border-bottom: none;
}

.order-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.order-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.order-time {
  font-size: 12px;
  color: #999;
}
</style>
