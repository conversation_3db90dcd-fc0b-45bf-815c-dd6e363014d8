<template>
  <Layout>
    <div class="work-order-detail">
    <h1>工单详情页面测试</h1>
    <p>Debug: 组件已加载</p>
    <p>Debug: loading = {{ loading }}</p>
    <p>Debug: workOrder = {{ workOrder ? 'exists' : 'null' }}</p>
    <p>Debug: route.params.id = {{ route.params.id }}</p>

    <el-card>
      <h2>基本测试</h2>
      <p>如果你能看到这个内容，说明组件正常渲染</p>
      <el-button @click="$router.back()">返回</el-button>
    </el-card>

    <div v-if="loading">
      <p>正在加载工单详情...</p>
    </div>

    <div v-else-if="workOrder">
      <el-card>
        <h2>{{ workOrder.title }}</h2>
        <p><strong>工单编号:</strong> {{ workOrder.order_number }}</p>
        <p><strong>描述:</strong> {{ workOrder.description }}</p>
        <p><strong>状态:</strong> {{ workOrder.status }}</p>
        <p><strong>优先级:</strong> {{ workOrder.priority }}</p>
        <p><strong>创建人:</strong> {{ workOrder.creator_name }}</p>
      </el-card>
    </div>

    <div v-else>
      <el-card>
        <p>Debug: 显示空状态</p>
        <p>工单不存在或加载失败</p>
      </el-card>
    </div>
  </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import Layout from '@/components/Layout.vue'

const route = useRoute()

const loading = ref(false)
const workOrder = ref<any>(null)

console.log('WorkOrderDetailView component loaded')
console.log('Route params:', route.params)

// 简单的测试数据
onMounted(() => {
  console.log('Component mounted')
  setTimeout(() => {
    workOrder.value = {
      id: 1,
      title: '测试工单',
      order_number: 'WO001',
      description: '这是一个测试工单',
      status: 'pending',
      priority: 'high',
      creator_name: 'admin'
    }
    loading.value = false
  }, 1000)
})
</script>

<style scoped>
.work-order-detail {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}
</style>
