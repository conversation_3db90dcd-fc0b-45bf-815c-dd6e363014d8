<template>
  <Layout>
    <div class="work-order-detail">
      <div class="page-header">
        <h1>工单详情</h1>
        <div class="header-actions">
          <el-button
            v-if="canEdit"
            type="primary"
            @click="$router.push(`/work-orders/${workOrder?.id}/edit`)"
          >
            编辑工单
          </el-button>
          <el-button @click="$router.back()">返回</el-button>
        </div>
      </div>

      <div v-if="loading" v-loading="loading" style="height: 200px;"></div>

      <div v-else-if="workOrder">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <div class="status-badges">
                <el-tag :type="getStatusType(workOrder.status)" size="large">
                  {{ getStatusText(workOrder.status) }}
                </el-tag>
                <el-tag :type="getPriorityType(workOrder.priority)" size="large">
                  {{ getPriorityText(workOrder.priority) }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="work-order-info">
            <h2 class="title">{{ workOrder.title }}</h2>
            <div class="description">
              <p>{{ workOrder.description }}</p>
            </div>

            <el-row :gutter="20" class="info-row">
              <el-col :span="8">
                <div class="info-item">
                  <label>创建人:</label>
                  <span>{{ workOrder.creator_name }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>负责人:</label>
                  <span>{{ workOrder.assignee_name || '未分配' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>所属分组:</label>
                  <span>{{ workOrder.group_name || '无' }}</span>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="info-row">
              <el-col :span="8">
                <div class="info-item">
                  <label>创建时间:</label>
                  <span>{{ formatTime(workOrder.created_at) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>更新时间:</label>
                  <span>{{ formatTime(workOrder.updated_at) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>截止时间:</label>
                  <span>{{ workOrder.due_date ? formatTime(workOrder.due_date) : '无' }}</span>
                </div>
              </el-col>
            </el-row>

            <div v-if="workOrder.completed_at" class="info-row">
              <el-col :span="8">
                <div class="info-item">
                  <label>完成时间:</label>
                  <span>{{ formatTime(workOrder.completed_at) }}</span>
                </div>
              </el-col>
            </div>
          </div>
        </el-card>

        <!-- 自定义字段 -->
        <el-card v-if="hasCustomFields" class="custom-fields-card">
          <template #header>
            <span>自定义字段</span>
          </template>

          <el-row :gutter="20">
            <el-col
              v-for="(value, key) in workOrder.custom_fields"
              :key="key"
              :span="8"
              class="custom-field-item"
            >
              <div class="info-item">
                <label>{{ getFieldLabel(key) }}:</label>
                <span>{{ formatCustomFieldValue(key, value) }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <div v-else>
        <el-card>
          <el-empty description="工单不存在" />
        </el-card>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import Layout from '@/components/Layout.vue'
import { getWorkOrder, getWorkOrderFields, type WorkOrder, type WorkOrderField } from '@/api/workOrders'
import { useAuthStore } from '@/stores/auth'
import dayjs from 'dayjs'

const route = useRoute()
const authStore = useAuthStore()

const loading = ref(false)
const workOrder = ref<WorkOrder | null>(null)
const customFields = ref<WorkOrderField[]>([])

const canEdit = computed(() => {
  if (!workOrder.value || !authStore.user) return false
  return authStore.isAdmin ||
         authStore.isManager ||
         workOrder.value.creator_id === authStore.user.id ||
         workOrder.value.assignee_id === authStore.user.id
})

const hasCustomFields = computed(() => {
  return workOrder.value?.custom_fields && Object.keys(workOrder.value.custom_fields).length > 0
})

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    assigned: 'info',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    assigned: '已分配',
    in_progress: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const getFieldLabel = (fieldName: string) => {
  const field = customFields.value.find(f => f.name === fieldName)
  return field?.label || fieldName
}

const formatCustomFieldValue = (fieldName: string, value: any) => {
  const field = customFields.value.find(f => f.name === fieldName)
  if (!field) return value

  if (field.field_type === 'boolean') {
    return value ? '是' : '否'
  }
  if (field.field_type === 'date') {
    return value ? dayjs(value).format('YYYY-MM-DD') : ''
  }
  return value || ''
}

const loadWorkOrder = async () => {
  const id = Number(route.params.id)
  if (!id) {
    ElMessage.error('无效的工单ID')
    return
  }

  loading.value = true
  try {
    const response = await getWorkOrder(id)
    workOrder.value = response.work_order
  } catch (error) {
    console.error('加载工单详情失败:', error)
    ElMessage.error('加载工单详情失败')
  } finally {
    loading.value = false
  }
}

const loadCustomFields = async () => {
  try {
    const response = await getWorkOrderFields()
    customFields.value = response.fields
  } catch (error) {
    console.error('加载自定义字段失败:', error)
  }
}

onMounted(() => {
  loadWorkOrder()
  loadCustomFields()
})
</script>

<style scoped>
.work-order-detail {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badges {
  display: flex;
  gap: 8px;
}

.work-order-info .title {
  margin: 0 0 16px 0;
  font-size: 20px;
  color: #333;
}

.work-order-info .description {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.work-order-info .description p {
  margin: 0;
  line-height: 1.6;
  color: #666;
}

.info-row {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.info-item span {
  color: #333;
}

.custom-fields-card {
  margin-bottom: 20px;
}

.custom-field-item {
  margin-bottom: 16px;
}
</style>
