<template>
  <div class="work-order-detail" style="padding: 20px; background: #f0f0f0; min-height: 100vh;">
    <h1 style="color: red; font-size: 32px; text-align: center;">🔧 工单详情页面测试</h1>

    <div style="background: white; padding: 20px; margin: 20px 0; border: 3px solid red;">
      <h2 style="color: blue;">📊 调试信息</h2>
      <p style="color: green; font-size: 18px;">✅ 组件已加载</p>
      <p style="color: orange; font-size: 18px;">📦 loading = {{ loading }}</p>
      <p style="color: purple; font-size: 18px;">🔗 route.params.id = {{ route.params.id }}</p>
      <p style="color: brown; font-size: 18px;">⏰ 当前时间 = {{ currentTime }}</p>
      <p style="color: navy; font-size: 18px;">📋 workOrder = {{ workOrder ? 'exists' : 'null' }}</p>
    </div>

    <div style="background: yellow; padding: 20px; margin: 20px 0; border: 3px solid blue;">
      <h2>🎯 基本测试</h2>
      <p style="font-size: 18px;">如果你能看到这个黄色背景的内容，说明组件正常渲染</p>
      <button @click="goBack" style="background: blue; color: white; padding: 15px 30px; font-size: 16px; border: none; cursor: pointer;">
        返回上一页
      </button>
    </div>

    <div v-if="loading" style="background: orange; padding: 20px; margin: 20px 0; border: 3px solid yellow;">
      <h2>⏳ 加载中...</h2>
      <p style="font-size: 18px;">正在加载工单详情...</p>
    </div>

    <div v-else-if="workOrder" style="background: lightgreen; padding: 20px; margin: 20px 0; border: 3px solid green;">
      <h2>📋 工单信息</h2>
      <p style="font-size: 18px;"><strong>标题:</strong> {{ workOrder.title }}</p>
      <p style="font-size: 18px;"><strong>工单编号:</strong> {{ workOrder.order_number }}</p>
      <p style="font-size: 18px;"><strong>描述:</strong> {{ workOrder.description }}</p>
      <p style="color: red; font-size: 18px;"><strong>状态:</strong> {{ workOrder.status }}</p>
      <p style="color: orange; font-size: 18px;"><strong>优先级:</strong> {{ workOrder.priority }}</p>
      <p style="color: blue; font-size: 18px;"><strong>创建人:</strong> {{ workOrder.creator_name }}</p>
    </div>

    <div v-else style="background: lightcoral; padding: 20px; margin: 20px 0; border: 3px solid red;">
      <h2>❌ 空状态</h2>
      <p style="font-size: 18px;">工单不存在或加载失败</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

console.log('🚀 WorkOrderDetailView component script loaded')

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const workOrder = ref<any>(null)
const currentTime = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const goBack = () => {
  console.log('🔙 Going back')
  router.back()
}

console.log('📍 Route params:', route.params)
console.log('🔗 Route path:', route.path)
console.log('📝 Route name:', route.name)

onMounted(() => {
  console.log('🎯 Component mounted')
  updateTime()

  // 更新时间每秒
  setInterval(updateTime, 1000)

  // 模拟加载数据
  setTimeout(() => {
    console.log('📦 Setting test data')
    workOrder.value = {
      id: route.params.id,
      title: '测试工单 - ' + route.params.id,
      order_number: 'WO' + route.params.id,
      description: '这是一个测试工单，用于验证页面渲染',
      status: 'pending',
      priority: 'high',
      creator_name: 'admin'
    }
    loading.value = false
    console.log('✅ Data loaded:', workOrder.value)
  }, 2000)
})
</script>

<style scoped>
.work-order-detail {
  font-family: Arial, sans-serif;
}

button:hover {
  background: darkblue !important;
}
</style>