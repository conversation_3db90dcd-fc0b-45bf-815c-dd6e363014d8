<template>
  <Layout>
    <div class="work-order-detail">
      <div class="page-header">
        <h1>工单详情</h1>
        <div class="header-actions">
          <el-button @click="$router.back()">返回</el-button>
        </div>
      </div>

      <div v-if="loading" v-loading="loading" style="height: 200px;">
        <p>正在加载工单详情...</p>
      </div>

      <div v-else-if="workOrder">
        <el-card>
          <h2>{{ workOrder.title }}</h2>
          <p><strong>工单编号:</strong> {{ workOrder.order_number }}</p>
          <p><strong>描述:</strong> {{ workOrder.description }}</p>
          <p><strong>状态:</strong> 
            <el-tag :type="getStatusType(workOrder.status)">
              {{ getStatusText(workOrder.status) }}
            </el-tag>
          </p>
          <p><strong>优先级:</strong> 
            <el-tag :type="getPriorityType(workOrder.priority)">
              {{ getPriorityText(workOrder.priority) }}
            </el-tag>
          </p>
          <p><strong>创建人:</strong> {{ workOrder.creator_name }}</p>
          <p><strong>负责人:</strong> {{ workOrder.assignee_name || '未分配' }}</p>
          <p><strong>创建时间:</strong> {{ formatTime(workOrder.created_at) }}</p>
          <p><strong>更新时间:</strong> {{ formatTime(workOrder.updated_at) }}</p>
        </el-card>
      </div>
      
      <div v-else>
        <el-card>
          <el-empty description="工单不存在" />
        </el-card>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import Layout from '@/components/Layout.vue'
import { getWorkOrder, type WorkOrder } from '@/api/workOrders'
import dayjs from 'dayjs'

const route = useRoute()

const loading = ref(false)
const workOrder = ref<WorkOrder | null>(null)

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    assigned: 'info',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    assigned: '已分配',
    in_progress: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const formatTime = (time: string) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : ''
}

const loadWorkOrder = async () => {
  const id = Number(route.params.id)
  if (!id) {
    ElMessage.error('无效的工单ID')
    return
  }

  loading.value = true
  try {
    console.log('Loading work order with ID:', id)
    const response = await getWorkOrder(id)
    console.log('Work order response:', response)
    workOrder.value = response.work_order
  } catch (error) {
    console.error('加载工单详情失败:', error)
    ElMessage.error('加载工单详情失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadWorkOrder()
})
</script>

<style scoped>
.work-order-detail {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}
</style>
