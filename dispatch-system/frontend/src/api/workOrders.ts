import api from './index'

export interface WorkOrder {
  id: number
  order_number: string
  title: string
  description: string
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assignee_id?: number
  assignee_name?: string
  creator_id: number
  creator_name: string
  group_id?: number
  group_name?: string
  created_at: string
  updated_at: string
  scheduled_start?: string
  scheduled_end?: string
  actual_start?: string
  actual_end?: string
  custom_fields?: Record<string, any>
}

export interface WorkOrderField {
  id: number
  name: string
  label: string
  field_type: 'text' | 'number' | 'date' | 'select' | 'textarea' | 'boolean'
  options?: string[]
  required: boolean
  order: number
  is_active: boolean
}

export interface CreateWorkOrderData {
  title: string
  description: string
  priority: string
  assignee_id?: number
  group_id?: number
  due_date?: string
  custom_fields?: Record<string, any>
}

export interface UpdateWorkOrderData extends Partial<CreateWorkOrderData> {
  status?: string
}

export interface WorkOrderListParams {
  page?: number
  per_page?: number
  search?: string
  status?: string
  priority?: string
  assignee_id?: number
  group_id?: number
}

export interface WorkOrderListResponse {
  work_orders: WorkOrder[]
  total: number
  current_page: number
  per_page?: number
  pages: number
}

// 获取工单列表
export const getWorkOrders = (params: WorkOrderListParams = {}): Promise<WorkOrderListResponse> => {
  return api.get('/work-orders', { params })
}

// 获取工单详情
export const getWorkOrder = (id: number): Promise<{ work_order: WorkOrder }> => {
  return api.get(`/work-orders/${id}`)
}

// 创建工单
export const createWorkOrder = (data: CreateWorkOrderData): Promise<{ work_order: WorkOrder }> => {
  return api.post('/work-orders', data)
}

// 更新工单
export const updateWorkOrder = (id: number, data: UpdateWorkOrderData): Promise<{ work_order: WorkOrder }> => {
  return api.put(`/work-orders/${id}`, data)
}

// 删除工单
export const deleteWorkOrder = (id: number): Promise<{ message: string }> => {
  return api.delete(`/work-orders/${id}`)
}

// 获取工单自定义字段配置
export const getWorkOrderFields = (): Promise<{ fields: WorkOrderField[] }> => {
  return api.get('/work-orders/fields')
}

// 创建自定义字段
export const createWorkOrderField = (data: Omit<WorkOrderField, 'id'>): Promise<{ field: WorkOrderField }> => {
  return api.post('/work-orders/fields', data)
}

// 更新自定义字段
export const updateWorkOrderField = (id: number, data: Partial<WorkOrderField>): Promise<{ field: WorkOrderField }> => {
  return api.put(`/work-orders/fields/${id}`, data)
}

// 删除自定义字段
export const deleteWorkOrderField = (id: number): Promise<{ message: string }> => {
  return api.delete(`/work-orders/fields/${id}`)
}

// 获取工单统计数据
export const getWorkOrderStats = (): Promise<{
  total: number
  pending: number
  assigned: number
  in_progress: number
  completed: number
  cancelled: number
}> => {
  return api.get('/work-orders/stats')
}
