from . import db
from datetime import datetime

class Material(db.Model):
    __tablename__ = 'materials'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(50), unique=True)  # 物料编码
    category = db.Column(db.String(50))  # 物料分类
    unit = db.Column(db.String(20))  # 单位：个、台、米、公斤等
    specification = db.Column(db.Text)  # 规格说明
    stock_quantity = db.Column(db.Float, default=0)  # 库存数量
    min_stock = db.Column(db.Float, default=0)  # 最小库存
    unit_price = db.Column(db.Float)  # 单价
    supplier = db.Column(db.String(100))  # 供应商
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'category': self.category,
            'unit': self.unit,
            'specification': self.specification,
            'stock_quantity': self.stock_quantity,
            'min_stock': self.min_stock,
            'unit_price': self.unit_price,
            'supplier': self.supplier,
            'is_active': self.is_active,
            'is_low_stock': self.stock_quantity <= self.min_stock if self.min_stock else False,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Material {self.name}>'
