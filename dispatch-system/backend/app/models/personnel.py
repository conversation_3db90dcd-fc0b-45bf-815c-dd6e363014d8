from . import db
from datetime import datetime

class Personnel(db.Model):
    __tablename__ = 'personnel'
    
    id = db.<PERSON>um<PERSON>(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    employee_id = db.Column(db.String(50), unique=True)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    position = db.Column(db.String(100))
    department = db.Column(db.String(100))
    skills = db.Column(db.Text)  # JSON格式存储技能列表
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    group_id = db.<PERSON>umn(db.Integer, db.<PERSON>('groups.id'), nullable=True)
    group = db.relationship('Group', backref='personnel')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'employee_id': self.employee_id,
            'phone': self.phone,
            'email': self.email,
            'position': self.position,
            'department': self.department,
            'skills': self.skills,
            'is_active': self.is_active,
            'group_id': self.group_id,
            'group_name': self.group.name if self.group else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Personnel {self.name}>'
