from . import db
from datetime import datetime
import json

class WorkOrder(db.Model):
    __tablename__ = 'work_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent
    status = db.Column(db.String(20), default='pending')  # pending, assigned, in_progress, completed, cancelled
    
    # 时间字段
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    scheduled_start = db.Column(db.DateTime)
    scheduled_end = db.Column(db.DateTime)
    actual_start = db.Column(db.DateTime)
    actual_end = db.Column(db.DateTime)
    
    # 关联关系
    creator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assignee_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'))
    
    creator = db.relationship('User', foreign_keys=[creator_id], backref='created_orders')
    assignee = db.relationship('User', foreign_keys=[assignee_id], backref='assigned_orders')
    group = db.relationship('Group', backref='work_orders')
    
    # 自定义字段值
    field_values = db.relationship('WorkOrderFieldValue', backref='work_order', cascade='all, delete-orphan')
    
    def to_dict(self, include_custom_fields=True):
        """转换为字典"""
        result = {
            'id': self.id,
            'order_number': self.order_number,
            'title': self.title,
            'description': self.description,
            'priority': self.priority,
            'status': self.status,
            'creator_id': self.creator_id,
            'creator_name': self.creator.username if self.creator else None,
            'assignee_id': self.assignee_id,
            'assignee_name': self.assignee.username if self.assignee else None,
            'group_id': self.group_id,
            'group_name': self.group.name if self.group else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'scheduled_start': self.scheduled_start.isoformat() if self.scheduled_start else None,
            'scheduled_end': self.scheduled_end.isoformat() if self.scheduled_end else None,
            'actual_start': self.actual_start.isoformat() if self.actual_start else None,
            'actual_end': self.actual_end.isoformat() if self.actual_end else None
        }
        
        if include_custom_fields:
            result['custom_fields'] = {}
            for field_value in self.field_values:
                result['custom_fields'][field_value.field.name] = {
                    'value': field_value.value,
                    'field_type': field_value.field.field_type,
                    'label': field_value.field.label
                }
        
        return result
    
    def __repr__(self):
        return f'<WorkOrder {self.order_number}>'


class WorkOrderField(db.Model):
    """工单自定义字段定义"""
    __tablename__ = 'work_order_fields'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)  # 字段名称（用于程序）
    label = db.Column(db.String(100), nullable=False)  # 字段标签（用于显示）
    field_type = db.Column(db.String(20), nullable=False)  # text, number, date, select, textarea, checkbox
    options = db.Column(db.Text)  # 选项（JSON格式，用于select类型）
    is_required = db.Column(db.Boolean, default=False)
    default_value = db.Column(db.Text)
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'label': self.label,
            'field_type': self.field_type,
            'options': json.loads(self.options) if self.options else None,
            'is_required': self.is_required,
            'default_value': self.default_value,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<WorkOrderField {self.name}>'


class WorkOrderFieldValue(db.Model):
    """工单自定义字段值"""
    __tablename__ = 'work_order_field_values'
    
    id = db.Column(db.Integer, primary_key=True)
    work_order_id = db.Column(db.Integer, db.ForeignKey('work_orders.id'), nullable=False)
    field_id = db.Column(db.Integer, db.ForeignKey('work_order_fields.id'), nullable=False)
    value = db.Column(db.Text)
    
    field = db.relationship('WorkOrderField', backref='field_values')
    
    __table_args__ = (db.UniqueConstraint('work_order_id', 'field_id'),)
    
    def __repr__(self):
        return f'<WorkOrderFieldValue {self.field.name}={self.value}>'
