from . import db
from datetime import datetime

class Group(db.Model):
    __tablename__ = 'groups'
    
    id = db.<PERSON>umn(db.<PERSON><PERSON>, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.<PERSON>umn(db.Text)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'user_count': len(self.users),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Group {self.name}>'
