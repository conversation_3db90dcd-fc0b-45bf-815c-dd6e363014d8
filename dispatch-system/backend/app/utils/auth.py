from functools import wraps
from flask import jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.user import User

def get_current_user():
    """获取当前登录用户"""
    current_user_id = get_jwt_identity()
    if current_user_id:
        return User.query.get(current_user_id)
    return None

def require_role(*allowed_roles):
    """权限装饰器"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user = get_current_user()
            
            if not current_user:
                return jsonify({'error': '用户不存在'}), 404
            
            if not current_user.is_active:
                return jsonify({'error': '账户已被禁用'}), 403
            
            if current_user.role not in allowed_roles:
                return jsonify({'error': '权限不足'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
