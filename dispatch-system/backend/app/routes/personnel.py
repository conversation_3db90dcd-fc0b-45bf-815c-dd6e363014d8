from flask import Blueprint, request, jsonify
from app.models import db
from app.models.personnel import Personnel
from app.utils.auth import require_role

personnel_bp = Blueprint('personnel', __name__)

@personnel_bp.route('', methods=['GET'])
@require_role('admin', 'manager', 'user')
def get_personnel():
    """获取人员列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '')
        group_id = request.args.get('group_id', type=int)
        
        query = Personnel.query.filter_by(is_active=True)
        
        if search:
            query = query.filter(
                db.or_(
                    Personnel.name.contains(search),
                    Personnel.employee_id.contains(search),
                    Personnel.phone.contains(search)
                )
            )
        
        if group_id:
            query = query.filter_by(group_id=group_id)
        
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'personnel': [person.to_dict() for person in pagination.items],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@personnel_bp.route('', methods=['POST'])
@require_role('admin', 'manager')
def create_personnel():
    """创建人员"""
    try:
        data = request.get_json()
        
        if not data.get('name'):
            return jsonify({'error': '姓名不能为空'}), 400
        
        # 检查工号是否已存在
        if data.get('employee_id'):
            if Personnel.query.filter_by(employee_id=data['employee_id']).first():
                return jsonify({'error': '工号已存在'}), 400
        
        personnel = Personnel(
            name=data['name'],
            employee_id=data.get('employee_id'),
            phone=data.get('phone'),
            email=data.get('email'),
            position=data.get('position'),
            department=data.get('department'),
            skills=data.get('skills'),
            group_id=data.get('group_id')
        )
        
        db.session.add(personnel)
        db.session.commit()
        
        return jsonify({'personnel': personnel.to_dict()}), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@personnel_bp.route('/<int:personnel_id>', methods=['PUT'])
@require_role('admin', 'manager')
def update_personnel(personnel_id):
    """更新人员"""
    try:
        personnel = Personnel.query.get_or_404(personnel_id)
        data = request.get_json()
        
        # 检查工号是否已被其他人员使用
        if 'employee_id' in data and data['employee_id'] != personnel.employee_id:
            if Personnel.query.filter_by(employee_id=data['employee_id']).first():
                return jsonify({'error': '工号已存在'}), 400
        
        # 更新字段
        for field in ['name', 'employee_id', 'phone', 'email', 'position', 
                     'department', 'skills', 'group_id', 'is_active']:
            if field in data:
                setattr(personnel, field, data[field])
        
        db.session.commit()
        
        return jsonify({'personnel': personnel.to_dict()}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@personnel_bp.route('/<int:personnel_id>', methods=['DELETE'])
@require_role('admin')
def delete_personnel(personnel_id):
    """删除人员"""
    try:
        personnel = Personnel.query.get_or_404(personnel_id)
        
        db.session.delete(personnel)
        db.session.commit()
        
        return jsonify({'message': '人员删除成功'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
