from flask import Blueprint, request, jsonify
from app.models import db
from app.models.work_order import WorkOrder, WorkOrderField, WorkOrderFieldValue
from app.utils.auth import require_role, get_current_user
from datetime import datetime
import json

work_orders_bp = Blueprint('work_orders', __name__)

@work_orders_bp.route('', methods=['GET'])
@require_role('admin', 'manager', 'user')
def get_work_orders():
    """获取工单列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '')
        status = request.args.get('status')
        priority = request.args.get('priority')
        assignee_id = request.args.get('assignee_id', type=int)
        group_id = request.args.get('group_id', type=int)
        
        current_user = get_current_user()
        query = WorkOrder.query
        
        # 根据用户角色过滤数据
        if current_user.role == 'user':
            # 普通用户只能看到自己创建的或分配给自己的工单
            query = query.filter(
                db.or_(
                    WorkOrder.creator_id == current_user.id,
                    WorkOrder.assignee_id == current_user.id
                )
            )
        elif current_user.role == 'manager':
            # 管理员可以看到自己组的工单
            if current_user.group_id:
                query = query.filter(WorkOrder.group_id == current_user.group_id)
        
        if search:
            query = query.filter(
                db.or_(
                    WorkOrder.order_number.contains(search),
                    WorkOrder.title.contains(search),
                    WorkOrder.description.contains(search)
                )
            )
        
        if status:
            query = query.filter_by(status=status)
        
        if priority:
            query = query.filter_by(priority=priority)
        
        if assignee_id:
            query = query.filter_by(assignee_id=assignee_id)
        
        if group_id:
            query = query.filter_by(group_id=group_id)
        
        query = query.order_by(WorkOrder.created_at.desc())
        
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'work_orders': [order.to_dict() for order in pagination.items],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@work_orders_bp.route('', methods=['POST'])
@require_role('admin', 'manager', 'user')
def create_work_order():
    """创建工单"""
    try:
        data = request.get_json()
        current_user = get_current_user()

        if not data.get('title'):
            return jsonify({'error': '工单标题不能为空'}), 400

        # 生成工单号
        from datetime import datetime
        order_number = f"WO{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 确保工单号唯一
        counter = 1
        base_number = order_number
        while WorkOrder.query.filter_by(order_number=order_number).first():
            order_number = f"{base_number}_{counter}"
            counter += 1

        work_order = WorkOrder(
            order_number=order_number,
            title=data['title'],
            description=data.get('description', ''),
            priority=data.get('priority', 'medium'),
            status=data.get('status', 'pending'),
            creator_id=current_user.id,
            assignee_id=data.get('assignee_id'),
            group_id=data.get('group_id'),
            scheduled_start=datetime.fromisoformat(data['scheduled_start']) if data.get('scheduled_start') else None,
            scheduled_end=datetime.fromisoformat(data['scheduled_end']) if data.get('scheduled_end') else None
        )

        db.session.add(work_order)
        db.session.flush()  # 获取工单ID

        # 处理自定义字段
        custom_fields = data.get('custom_fields', {})
        for field_name, field_value in custom_fields.items():
            field = WorkOrderField.query.filter_by(name=field_name, is_active=True).first()
            if field:
                field_value_obj = WorkOrderFieldValue(
                    work_order_id=work_order.id,
                    field_id=field.id,
                    value=str(field_value) if field_value is not None else None
                )
                db.session.add(field_value_obj)

        db.session.commit()

        return jsonify({'work_order': work_order.to_dict()}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@work_orders_bp.route('/<int:order_id>', methods=['GET'])
@require_role('admin', 'manager', 'user')
def get_work_order(order_id):
    """获取工单详情"""
    try:
        current_user = get_current_user()
        work_order = WorkOrder.query.get_or_404(order_id)

        # 权限检查
        if current_user.role == 'user':
            if work_order.creator_id != current_user.id and work_order.assignee_id != current_user.id:
                return jsonify({'error': '权限不足'}), 403
        elif current_user.role == 'manager':
            if current_user.group_id and work_order.group_id != current_user.group_id:
                return jsonify({'error': '权限不足'}), 403

        return jsonify({'work_order': work_order.to_dict()}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@work_orders_bp.route('/<int:order_id>', methods=['PUT'])
@require_role('admin', 'manager', 'user')
def update_work_order(order_id):
    """更新工单"""
    try:
        current_user = get_current_user()
        work_order = WorkOrder.query.get_or_404(order_id)
        data = request.get_json()

        # 权限检查
        if current_user.role == 'user':
            if work_order.creator_id != current_user.id and work_order.assignee_id != current_user.id:
                return jsonify({'error': '权限不足'}), 403
        elif current_user.role == 'manager':
            if current_user.group_id and work_order.group_id != current_user.group_id:
                return jsonify({'error': '权限不足'}), 403

        # 更新基本字段
        for field in ['title', 'description', 'priority', 'status', 'assignee_id', 'group_id']:
            if field in data:
                setattr(work_order, field, data[field])

        # 更新时间字段
        for field in ['scheduled_start', 'scheduled_end', 'actual_start', 'actual_end']:
            if field in data and data[field]:
                setattr(work_order, field, datetime.fromisoformat(data[field]))
            elif field in data and data[field] is None:
                setattr(work_order, field, None)

        # 更新自定义字段
        if 'custom_fields' in data:
            # 删除现有的自定义字段值
            WorkOrderFieldValue.query.filter_by(work_order_id=work_order.id).delete()

            # 添加新的自定义字段值
            custom_fields = data['custom_fields']
            for field_name, field_value in custom_fields.items():
                field = WorkOrderField.query.filter_by(name=field_name, is_active=True).first()
                if field:
                    field_value_obj = WorkOrderFieldValue(
                        work_order_id=work_order.id,
                        field_id=field.id,
                        value=str(field_value) if field_value is not None else None
                    )
                    db.session.add(field_value_obj)

        db.session.commit()

        return jsonify({'work_order': work_order.to_dict()}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@work_orders_bp.route('/<int:order_id>', methods=['DELETE'])
@require_role('admin', 'manager')
def delete_work_order(order_id):
    """删除工单"""
    try:
        work_order = WorkOrder.query.get_or_404(order_id)
        current_user = get_current_user()

        # 权限检查
        if current_user.role == 'manager':
            if current_user.group_id and work_order.group_id != current_user.group_id:
                return jsonify({'error': '权限不足'}), 403

        db.session.delete(work_order)
        db.session.commit()

        return jsonify({'message': '工单删除成功'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 自定义字段管理
@work_orders_bp.route('/fields', methods=['GET'])
@require_role('admin', 'manager', 'user')
def get_work_order_fields():
    """获取工单自定义字段列表"""
    try:
        fields = WorkOrderField.query.filter_by(is_active=True).order_by(WorkOrderField.sort_order).all()

        return jsonify({
            'fields': [field.to_dict() for field in fields]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@work_orders_bp.route('/fields', methods=['POST'])
@require_role('admin', 'manager')
def create_work_order_field():
    """创建工单自定义字段"""
    try:
        data = request.get_json()

        if not data.get('name') or not data.get('label'):
            return jsonify({'error': '字段名称和标签不能为空'}), 400

        # 检查字段名是否已存在
        if WorkOrderField.query.filter_by(name=data['name']).first():
            return jsonify({'error': '字段名称已存在'}), 400

        field = WorkOrderField(
            name=data['name'],
            label=data['label'],
            field_type=data.get('field_type', 'text'),
            options=json.dumps(data['options']) if data.get('options') else None,
            is_required=data.get('is_required', False),
            default_value=data.get('default_value'),
            sort_order=data.get('sort_order', 0)
        )

        db.session.add(field)
        db.session.commit()

        return jsonify({'field': field.to_dict()}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@work_orders_bp.route('/fields/<int:field_id>', methods=['PUT'])
@require_role('admin', 'manager')
def update_work_order_field(field_id):
    """更新工单自定义字段"""
    try:
        field = WorkOrderField.query.get_or_404(field_id)
        data = request.get_json()

        # 检查字段名是否已被其他字段使用
        if 'name' in data and data['name'] != field.name:
            if WorkOrderField.query.filter_by(name=data['name']).first():
                return jsonify({'error': '字段名称已存在'}), 400

        # 更新字段
        for attr in ['name', 'label', 'field_type', 'is_required', 'default_value', 'sort_order', 'is_active']:
            if attr in data:
                setattr(field, attr, data[attr])

        if 'options' in data:
            field.options = json.dumps(data['options']) if data['options'] else None

        db.session.commit()

        return jsonify({'field': field.to_dict()}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@work_orders_bp.route('/fields/<int:field_id>', methods=['DELETE'])
@require_role('admin')
def delete_work_order_field(field_id):
    """删除工单自定义字段"""
    try:
        field = WorkOrderField.query.get_or_404(field_id)

        # 删除相关的字段值
        WorkOrderFieldValue.query.filter_by(field_id=field.id).delete()

        db.session.delete(field)
        db.session.commit()

        return jsonify({'message': '字段删除成功'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
